# 🖥️ CLI Command Reference

## 🚀 Quick Start

```bash
# 1. Setup (first time only)
python3 setup.py

# 2. Start crawler
python3 crawler.py

# 3. Enter URL and press Enter
example.com

# 4. Exit when done
quit
```

## 📋 Complete Command List

### Setup & Installation

| Command | Description | When to Use |
|---------|-------------|-------------|
| `python3 setup.py` | Install dependencies and test setup | First time setup |
| `pip install -r requirements.txt` | Install dependencies manually | If setup.py fails |

### Running the Crawler

| Command | Description | Output |
|---------|-------------|--------|
| `python3 crawler.py` | Start interactive crawler | Opens interactive prompt |
| `echo "URL" \| python3 crawler.py` | Single URL crawl | Crawls URL and exits |

### Interactive Mode Commands

| Input | Action | Example |
|-------|--------|---------|
| `<URL>` | Crawl the URL | `example.com` |
| `quit` | Exit program | `quit` |
| `exit` | Exit program | `exit` |
| `q` | Exit program | `q` |
| `<Enter>` (empty) | Show "Please enter a valid URL" | |

### URL Input Formats

| Input Format | Processed As | Notes |
|--------------|--------------|-------|
| `example.com` | `https://example.com` | Auto-adds https:// |
| `subdomain.example.com` | `https://subdomain.example.com` | Auto-adds https:// |
| `http://example.com` | `http://example.com` | Keeps original protocol |
| `https://example.com` | `https://example.com` | Keeps original protocol |
| `ftp://example.com` | `https://ftp://example.com` | ⚠️ Will add https:// (may fail) |

## 🔧 Advanced Usage

### Batch Processing
```bash
# Create a script for multiple URLs
cat > batch_crawl.py << 'EOF'
#!/usr/bin/env python3
from crawler import crawl_url

urls = ["example.com", "httpbin.org/html", "news.ycombinator.com"]
for url in urls:
    print(f"\n=== Crawling {url} ===")
    result = crawl_url(url)
    print(result[:200] + "..." if len(result) > 200 else result)
EOF

python3 batch_crawl.py
```

### One-liner Tests
```bash
# Test with different sites
echo "example.com" | python3 crawler.py
echo "httpbin.org/html" | python3 crawler.py  
echo "news.ycombinator.com" | python3 crawler.py
```

### Programmatic Usage
```python
# In your Python script
from crawler import crawl_url

# Simple usage
content = crawl_url("https://example.com")
print(content)

# With error handling
result = crawl_url("https://example.com")
if result.startswith("Error"):
    print("Failed to crawl")
else:
    print(f"Success! Got {len(result)} characters")
```

## 📊 Status Messages & Responses

### Success Flow
```
Enter URL (or 'quit' to exit): example.com

Crawling: https://example.com
--------------------------------------------------
Trying fast engine (crawl4ai)...
✓ Success with fast engine

Result:
==================================================
# Example Domain

This domain is for use in illustrative examples...
==================================================
```

### Fallback Flow
```
Enter URL (or 'quit' to exit): complex-js-site.com

Crawling: https://complex-js-site.com
--------------------------------------------------
Trying fast engine (crawl4ai)...
✗ Fast engine failed: Error fetching URL: ...
Falling back to Selenium engine (handles JavaScript)...
✓ Success with Selenium engine

Result:
==================================================
# Complex Site Title

Dynamic content loaded by JavaScript...
==================================================
```

### Error Flow
```
Enter URL (or 'quit' to exit): invalid-site.com

Crawling: https://invalid-site.com
--------------------------------------------------
Trying fast engine (crawl4ai)...
✗ Fast engine failed: Error fetching URL: ...
Falling back to Selenium engine (handles JavaScript)...
✗ Selenium engine failed: Error fetching URL with Selenium: ...
```

## 🛠️ Troubleshooting Commands

### Check Installation
```bash
# Verify Python version
python3 --version

# Check if modules are installed
python3 -c "import requests, bs4, selenium; print('All modules OK')"

# Test ChromeDriver (for Selenium)
python3 -c "from webdriver_manager.chrome import ChromeDriverManager; print('ChromeDriver OK')"
```

### Fix Common Issues
```bash
# Reinstall dependencies
pip3 install --upgrade -r requirements.txt

# Clear Python cache
rm -rf __pycache__

# Update ChromeDriver
python3 -c "from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"
```

### Debug Mode
```python
# Add debug prints to crawler.py if needed
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🎯 Use Case Examples

### Content Research
```bash
python3 crawler.py
# Enter: news.ycombinator.com
# Enter: reddit.com/r/programming
# Enter: quit
```

### Website Analysis
```bash
# Test multiple pages of same site
python3 crawler.py
# Enter: example.com
# Enter: example.com/about
# Enter: example.com/contact
# Enter: quit
```

### Comparison Testing
```bash
# Compare static vs dynamic sites
python3 crawler.py
# Enter: example.com (static - uses crawl4ai)
# Enter: gmail.com (dynamic - uses Selenium)
# Enter: quit
```

## 📈 Performance Tips

### For Speed
- Use simple URLs when possible (triggers fast engine)
- Avoid heavily JavaScript-dependent sites if speed is priority
- Close and restart crawler periodically for long sessions

### For Reliability  
- Let both engines try (don't interrupt)
- Use full URLs with protocol when possible
- Test with simple sites first (example.com)

## 🔍 Engine Selection Guide

### When crawl4ai (fast) engine is used:
- Static HTML sites
- News websites
- Blogs and articles
- Simple e-commerce pages
- Documentation sites

### When Selenium (slow) engine is used:
- Single Page Applications (SPAs)
- Sites requiring JavaScript
- Dynamic content loading
- Complex interactive sites
- Sites that block simple HTTP requests

## 🚨 Error Codes & Solutions

| Error Message | Cause | Solution |
|---------------|-------|----------|
| `Command not found: python3` | Python not installed | Install Python 3.7+ |
| `ModuleNotFoundError` | Dependencies missing | Run `python3 setup.py` |
| `ChromeDriver not found` | Chrome/ChromeDriver issue | Install Chrome browser |
| `Connection timeout` | Network/site issue | Check internet, try different URL |
| `Both engines failed` | Site blocks crawlers | Try different site or check URL |

## 🎉 Success Indicators

✅ **Good signs:**
- `✓ Success with fast engine` - Optimal performance
- `✓ Success with Selenium engine` - Handled complex site
- Content length > 100 characters - Good extraction
- Clean formatted output - Proper content processing

⚠️ **Warning signs:**
- Very short content - May need manual verification
- Multiple fallbacks - Site may be problematic
- Timeout messages - Network or site issues

❌ **Error signs:**
- `Both engines failed` - Site inaccessible or blocks crawlers
- Import errors - Setup issues
- Crash/exception - Bug or environment issue
