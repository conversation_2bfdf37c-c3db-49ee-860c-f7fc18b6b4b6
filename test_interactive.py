#!/usr/bin/env python3
"""
Test the interactive functionality of the crawler
Simulates user input to test the main interactive loop
"""

import sys
import os
from unittest.mock import patch
from io import StringIO

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import crawler

def test_interactive_session():
    """Test a complete interactive session"""
    print("Testing interactive session...")
    
    # Simulate user inputs: URL, then quit
    user_inputs = [
        "example.com",  # Test URL without protocol
        "quit"          # Exit command
    ]
    
    # Capture output
    captured_output = StringIO()
    
    with patch('builtins.input', side_effect=user_inputs):
        with patch('sys.stdout', captured_output):
            try:
                crawler.main()
            except SystemExit:
                pass  # Expected when quitting
    
    output = captured_output.getvalue()
    
    # Check that the session worked correctly
    success_indicators = [
        "Web Crawler",
        "Enter URL",
        "Crawling: https://example.com",  # Should add https://
        "Trying fast engine",
        "Success with fast engine",
        "Example Domain",
        "Goodbye!"
    ]
    
    passed = 0
    for indicator in success_indicators:
        if indicator in output:
            passed += 1
            print(f"✅ Found: {indicator}")
        else:
            print(f"❌ Missing: {indicator}")
    
    print(f"\nInteractive test: {passed}/{len(success_indicators)} indicators found")
    return passed == len(success_indicators)

def test_url_protocol_addition():
    """Test that URLs without protocol get https:// added"""
    print("\nTesting URL protocol addition...")
    
    test_cases = [
        ("example.com", "https://example.com"),
        ("www.google.com", "https://www.google.com"),
        ("http://example.com", "http://example.com"),
        ("https://example.com", "https://example.com"),
    ]
    
    all_passed = True
    for input_url, expected in test_cases:
        # Simulate the logic from main()
        if not input_url.startswith(('http://', 'https://')):
            processed_url = 'https://' + input_url
        else:
            processed_url = input_url
        
        if processed_url == expected:
            print(f"✅ {input_url} -> {processed_url}")
        else:
            print(f"❌ {input_url} -> {processed_url} (expected {expected})")
            all_passed = False
    
    return all_passed

def test_quit_commands():
    """Test various quit commands"""
    print("\nTesting quit commands...")
    
    quit_commands = ['quit', 'exit', 'q', 'QUIT', 'EXIT', 'Q']
    
    for cmd in quit_commands:
        # Test that the command is recognized as a quit command
        is_quit = cmd.lower() in ['quit', 'exit', 'q']
        if is_quit:
            print(f"✅ '{cmd}' recognized as quit command")
        else:
            print(f"❌ '{cmd}' not recognized as quit command")
    
    return True

def main():
    """Run all interactive tests"""
    print("Interactive Functionality Tests")
    print("=" * 40)
    
    tests = [
        test_interactive_session,
        test_url_protocol_addition,
        test_quit_commands
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n{'='*40}")
    print(f"Results: {passed}/{total} interactive tests passed")
    
    if passed == total:
        print("🎉 All interactive tests passed!")
        return True
    else:
        print("⚠️  Some tests failed - check output above")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
