#!/usr/bin/env python3
"""
Real-world integration tests for the web crawler
Tests against actual websites to ensure functionality works in practice
"""

import sys
import os
import time

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import crawler
import crawl4ai_engine
import selenium_engine

def test_simple_website():
    """Test crawling a simple, reliable website"""
    print("Testing simple website (example.com)...")
    url = "https://example.com"
    
    try:
        result = crawler.crawl_url(url)
        
        if result.startswith("Error"):
            print(f"❌ Failed: {result[:100]}...")
            return False
        
        # Check for expected content
        if "Example Domain" in result and len(result) > 50:
            print("✅ Simple website test passed")
            print(f"Content length: {len(result)} characters")
            return True
        else:
            print(f"❌ Unexpected content: {result[:100]}...")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_crawl4ai_engine_directly():
    """Test crawl4ai engine directly"""
    print("\nTesting crawl4ai engine directly...")
    url = "https://httpbin.org/html"
    
    try:
        result = crawl4ai_engine.get_clean_content(url)
        
        if result.startswith("Error"):
            print(f"❌ Failed: {result[:100]}...")
            return False
        
        if len(result) > 20:
            print("✅ Crawl4ai engine test passed")
            print(f"Content length: {len(result)} characters")
            return True
        else:
            print(f"❌ Content too short: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_selenium_engine_directly():
    """Test selenium engine directly"""
    print("\nTesting selenium engine directly...")
    url = "https://httpbin.org/html"
    
    try:
        result = selenium_engine.get_clean_content(url)
        
        if result.startswith("Error"):
            print(f"❌ Failed: {result[:100]}...")
            return False
        
        if len(result) > 20:
            print("✅ Selenium engine test passed")
            print(f"Content length: {len(result)} characters")
            return True
        else:
            print(f"❌ Content too short: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_javascript_heavy_site():
    """Test a site that requires JavaScript (should fallback to Selenium)"""
    print("\nTesting JavaScript-heavy site...")
    # Using a simple site that should work with both engines
    url = "https://httpbin.org/json"
    
    try:
        result = crawler.crawl_url(url)
        
        if result.startswith("Error"):
            print(f"❌ Failed: {result[:100]}...")
            return False
        
        if len(result) > 10:
            print("✅ JavaScript site test passed")
            print(f"Content length: {len(result)} characters")
            return True
        else:
            print(f"❌ Content too short: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_invalid_url():
    """Test handling of invalid URLs"""
    print("\nTesting invalid URL handling...")
    url = "https://this-domain-definitely-does-not-exist-12345.com"
    
    try:
        result = crawler.crawl_url(url)
        
        if result.startswith("Error"):
            print("✅ Invalid URL test passed - correctly returned error")
            return True
        else:
            print(f"❌ Should have failed but got: {result[:100]}...")
            return False
            
    except Exception as e:
        print(f"✅ Invalid URL test passed - correctly raised exception: {e}")
        return True

def main():
    """Run all real-world tests"""
    print("Real-World Web Crawler Tests")
    print("=" * 40)
    
    tests = [
        test_simple_website,
        test_crawl4ai_engine_directly,
        test_selenium_engine_directly,
        test_javascript_heavy_site,
        test_invalid_url
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            time.sleep(1)  # Be nice to servers
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n{'='*40}")
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All real-world tests passed!")
        return True
    else:
        print("⚠️  Some tests failed - check output above")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
