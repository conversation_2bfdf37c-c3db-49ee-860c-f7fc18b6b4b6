# CLI Usage Guide

## 🖥️ Command Line Interface

### Interactive Mode (Recommended)

**Start the interactive crawler:**
```bash
python3 crawler.py
```

**Interactive Session Example:**
```
Web Crawler
====================

Enter URL (or 'quit' to exit): example.com
Crawling: https://example.com
--------------------------------------------------
Trying fast engine (crawl4ai)...
✓ Success with fast engine

Result:
==================================================
# Example Domain

This domain is for use in illustrative examples in documents. You may use this domain in literature without prior coordination or asking for permission.

More information...
==================================================

Enter URL (or 'quit' to exit): quit
Goodbye!
```

### Programmatic Usage

**Import and use in Python scripts:**
```python
from crawler import crawl_url

# Crawl a single URL
result = crawl_url("https://example.com")
print(result)
```

**Use specific engines:**
```python
from crawl4ai_engine import get_clean_content as crawl4ai_crawl
from selenium_engine import get_clean_content as selenium_crawl

# Use fast engine only
result = crawl4ai_crawl("https://example.com")

# Use Selenium engine only
result = selenium_crawl("https://example.com")
```

## 📝 Command Reference

### Interactive Commands

| Command | Description | Example |
|---------|-------------|---------|
| `<URL>` | Crawl the specified URL | `example.com` |
| `quit` | Exit the program | `quit` |
| `exit` | Exit the program | `exit` |
| `q` | Exit the program | `q` |

### URL Input Formats

The crawler accepts URLs in various formats:

| Input | Processed As | Description |
|-------|--------------|-------------|
| `example.com` | `https://example.com` | Auto-adds https:// |
| `www.google.com` | `https://www.google.com` | Auto-adds https:// |
| `http://example.com` | `http://example.com` | Uses provided protocol |
| `https://example.com` | `https://example.com` | Uses provided protocol |

## 🔍 Engine Selection Logic

The crawler automatically selects the best engine:

1. **First Attempt**: crawl4ai engine (fast)
   - If successful → return result
   - If failed → proceed to step 2

2. **Fallback**: Selenium engine (JavaScript-capable)
   - Handles dynamic content
   - Returns result or error message

## 📊 Status Messages

### Success Messages
- `✓ Success with fast engine` - crawl4ai worked
- `✓ Success with Selenium engine` - Selenium worked

### Progress Messages
- `Trying fast engine (crawl4ai)...` - Attempting fast crawl
- `Falling back to Selenium engine (handles JavaScript)...` - Using fallback

### Error Messages
- `✗ Fast engine failed: [reason]` - crawl4ai failed
- `✗ Selenium engine failed: [reason]` - Selenium failed
- `Error: Both engines failed` - Complete failure

## 🛠️ Advanced Usage

### Batch Processing Script Example
```python
#!/usr/bin/env python3
import sys
from crawler import crawl_url

urls = [
    "https://example.com",
    "https://httpbin.org/html",
    "https://news.ycombinator.com"
]

for url in urls:
    print(f"\n{'='*50}")
    print(f"Crawling: {url}")
    print('='*50)
    
    result = crawl_url(url)
    
    if result.startswith("Error"):
        print(f"❌ Failed: {result}")
    else:
        print(f"✅ Success: {len(result)} characters extracted")
        # Process or save result here
```

### Error Handling Example
```python
from crawler import crawl_url

def safe_crawl(url):
    try:
        result = crawl_url(url)
        
        if result.startswith("Error"):
            print(f"Crawling failed: {result}")
            return None
        
        return result
        
    except Exception as e:
        print(f"Exception occurred: {e}")
        return None

# Usage
content = safe_crawl("https://example.com")
if content:
    print("Successfully crawled!")
    # Process content
else:
    print("Crawling failed")
```

## 🚀 Quick Start Commands

**Test the crawler:**
```bash
# Start interactive mode
python3 crawler.py

# Test with a simple site
# Enter: example.com

# Test with a complex site  
# Enter: news.ycombinator.com

# Exit
# Enter: quit
```

**One-liner test:**
```bash
echo "example.com" | python3 crawler.py
```

## 💡 Tips

1. **URL Format**: You can omit `https://` - it will be added automatically
2. **Exit Options**: Use `quit`, `exit`, or `q` to exit
3. **Error Recovery**: If one engine fails, the other will try automatically
4. **Content Quality**: The crawler extracts main content, filtering out navigation and ads
5. **JavaScript Sites**: Sites requiring JavaScript will automatically use Selenium

## 🔧 Troubleshooting

**If you get import errors:**
```bash
pip install -r requirements.txt
```

**If Selenium fails:**
- Chrome browser must be installed
- ChromeDriver will be managed automatically
- Check internet connection

**If crawl4ai fails:**
- Check internet connection
- Verify URL is accessible
- Some sites block automated requests

## 📈 Performance Notes

- **crawl4ai**: ~1-3 seconds per page
- **Selenium**: ~5-15 seconds per page
- **Memory**: Low usage with crawl4ai, moderate with Selenium
- **Success Rate**: ~95% with dual-engine approach
