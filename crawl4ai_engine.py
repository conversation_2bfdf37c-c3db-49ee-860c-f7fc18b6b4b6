import requests
from bs4 import BeautifulSoup
from readability import Document

def get_clean_content(url):
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
    except Exception as e:
        return f"Error fetching URL: {e}"

    doc = Document(response.text)
    title = doc.title()
    summary_html = doc.summary()
    soup = BeautifulSoup(summary_html, 'html.parser')

    # Convert to terminal-friendly markdown-like text
    content = f"# {title}\n\n"
    for elem in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol']):
        if elem.name.startswith('h'):
            level = int(elem.name[1])
            content += f"{'#' * level} {elem.get_text(strip=True)}\n\n"
        elif elem.name == 'p':
            text = elem.get_text(strip=True)
            if text:
                content += f"{text}\n\n"
        elif elem.name in ['ul', 'ol']:
            for li in elem.find_all('li'):
                content += f"* {li.get_text(strip=True)}\n"
            content += "\n"
    return content.strip()
