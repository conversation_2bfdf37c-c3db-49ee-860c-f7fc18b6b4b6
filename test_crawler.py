#!/usr/bin/env python3
"""
Comprehensive test suite for the web crawler project
Tests both crawl4ai and selenium engines with various scenarios
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock, Mock
import requests
from bs4 import BeautifulSoup

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import crawl4ai_engine
import selenium_engine
import crawler

class TestCrawl4aiEngine(unittest.TestCase):
    """Test the crawl4ai engine functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_url = "https://example.com"
        self.sample_html = """
        <html>
            <head><title>Test Page</title></head>
            <body>
                <h1>Main Title</h1>
                <p>This is a test paragraph.</p>
                <h2>Subtitle</h2>
                <p>Another paragraph with content.</p>
                <ul>
                    <li>List item 1</li>
                    <li>List item 2</li>
                </ul>
            </body>
        </html>
        """
    
    @patch('crawl4ai_engine.requests.get')
    def test_successful_crawl(self, mock_get):
        """Test successful content extraction"""
        # Mock successful response
        mock_response = Mock()
        mock_response.text = self.sample_html
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = crawl4ai_engine.get_clean_content(self.test_url)
        
        # Verify the result contains expected content
        self.assertIn("Test Page", result)
        self.assertIn("Main Title", result)
        self.assertIn("test paragraph", result)
        self.assertIn("Subtitle", result)
        # Note: List items might not be included in readability extraction
        
        # Verify requests was called correctly
        mock_get.assert_called_once_with(self.test_url, timeout=10)
    
    @patch('crawl4ai_engine.requests.get')
    def test_request_timeout(self, mock_get):
        """Test handling of request timeout"""
        mock_get.side_effect = requests.exceptions.Timeout("Request timed out")
        
        result = crawl4ai_engine.get_clean_content(self.test_url)
        
        self.assertTrue(result.startswith("Error fetching URL:"))
        self.assertIn("timed out", result)
    
    @patch('crawl4ai_engine.requests.get')
    def test_http_error(self, mock_get):
        """Test handling of HTTP errors"""
        mock_response = Mock()
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Not Found")
        mock_get.return_value = mock_response
        
        result = crawl4ai_engine.get_clean_content(self.test_url)
        
        self.assertTrue(result.startswith("Error fetching URL:"))
        self.assertIn("404", result)
    
    @patch('crawl4ai_engine.requests.get')
    def test_empty_content(self, mock_get):
        """Test handling of empty or minimal content"""
        mock_response = Mock()
        mock_response.text = "<html><head><title>Empty</title></head><body></body></html>"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = crawl4ai_engine.get_clean_content(self.test_url)
        
        # Should still return title even with empty body
        self.assertIn("Empty", result)

class TestSeleniumEngine(unittest.TestCase):
    """Test the selenium engine functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_url = "https://example.com"
        self.sample_html = """
        <html>
            <head><title>JS Test Page</title></head>
            <body>
                <h1>Dynamic Content</h1>
                <p>This content was loaded by JavaScript.</p>
                <div id="dynamic">Dynamic element</div>
            </body>
        </html>
        """
    
    @patch('selenium_engine.webdriver.Chrome')
    @patch('webdriver_manager.chrome.ChromeDriverManager')
    def test_successful_selenium_crawl(self, mock_driver_manager, mock_chrome):
        """Test successful content extraction with Selenium"""
        # Mock ChromeDriverManager
        mock_driver_manager.return_value.install.return_value = "/path/to/chromedriver"
        
        # Mock WebDriver
        mock_driver = Mock()
        mock_driver.page_source = self.sample_html
        mock_chrome.return_value = mock_driver
        
        result = selenium_engine.get_clean_content(self.test_url)
        
        # Verify the result contains expected content
        self.assertIn("JS Test Page", result)
        self.assertIn("Dynamic Content", result)
        self.assertIn("JavaScript", result)
        
        # Verify driver methods were called
        mock_driver.set_page_load_timeout.assert_called_once_with(15)
        mock_driver.get.assert_called_once_with(self.test_url)
        mock_driver.quit.assert_called()
    
    @patch('selenium_engine.webdriver.Chrome')
    def test_selenium_driver_error(self, mock_chrome):
        """Test handling of WebDriver initialization errors"""
        mock_chrome.side_effect = Exception("ChromeDriver not found")
        
        result = selenium_engine.get_clean_content(self.test_url)
        
        self.assertTrue(result.startswith("Error fetching URL with Selenium:"))
        self.assertIn("ChromeDriver not found", result)
    
    @patch('selenium_engine.webdriver.Chrome')
    def test_selenium_page_load_timeout(self, mock_chrome):
        """Test handling of page load timeout"""
        mock_driver = Mock()
        mock_driver.get.side_effect = Exception("Page load timeout")
        mock_chrome.return_value = mock_driver
        
        result = selenium_engine.get_clean_content(self.test_url)
        
        self.assertTrue(result.startswith("Error fetching URL with Selenium:"))
        self.assertIn("timeout", result)
        mock_driver.quit.assert_called()

class TestCrawlerMain(unittest.TestCase):
    """Test the main crawler functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_url = "https://example.com"
    
    @patch('crawl4ai_engine.get_clean_content')
    def test_crawl_url_success_with_crawl4ai(self, mock_crawl4ai):
        """Test successful crawling with crawl4ai engine"""
        # Mock crawl4ai success
        mock_crawl4ai.return_value = "# Test Content\n\nThis is test content."

        with patch('builtins.print') as mock_print:
            result = crawler.crawl_url(self.test_url)

        self.assertEqual(result, "# Test Content\n\nThis is test content.")
        # Verify success message was printed
        mock_print.assert_any_call("✓ Success with fast engine")

    @patch('crawl4ai_engine.get_clean_content')
    @patch('selenium_engine.get_clean_content')
    def test_crawl_url_fallback_to_selenium(self, mock_selenium, mock_crawl4ai):
        """Test fallback to selenium when crawl4ai fails"""
        # Mock crawl4ai failure and selenium success
        mock_crawl4ai.return_value = "Error: Failed to fetch"
        mock_selenium.return_value = "# Selenium Content\n\nContent from selenium."

        with patch('builtins.print') as mock_print:
            result = crawler.crawl_url(self.test_url)

        self.assertEqual(result, "# Selenium Content\n\nContent from selenium.")
        # Verify fallback message was printed
        mock_print.assert_any_call("Falling back to Selenium engine (handles JavaScript)...")
    
    def test_url_protocol_handling(self):
        """Test URL protocol handling in main function"""
        # Test that URLs without protocol get https:// added
        test_cases = [
            ("example.com", "https://example.com"),
            ("www.example.com", "https://www.example.com"),
            ("http://example.com", "http://example.com"),
            ("https://example.com", "https://example.com"),
        ]
        
        for input_url, expected_url in test_cases:
            # This tests the logic in main() function
            if not input_url.startswith(('http://', 'https://')):
                processed_url = 'https://' + input_url
            else:
                processed_url = input_url
            
            self.assertEqual(processed_url, expected_url)

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    @patch('crawl4ai_engine.requests.get')
    def test_end_to_end_crawl4ai(self, mock_get):
        """Test end-to-end functionality with crawl4ai"""
        # Mock a realistic HTML response
        html_content = """
        <html>
            <head><title>Real Website</title></head>
            <body>
                <article>
                    <h1>Article Title</h1>
                    <p>This is the main content of the article.</p>
                    <h2>Section Header</h2>
                    <p>More detailed information here.</p>
                </article>
            </body>
        </html>
        """
        
        mock_response = Mock()
        mock_response.text = html_content
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = crawler.crawl_url("https://example.com")
        
        # Verify complete pipeline works
        self.assertIn("Real Website", result)
        self.assertIn("Article Title", result)
        self.assertIn("main content", result)
        self.assertIn("Section Header", result)

if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)
