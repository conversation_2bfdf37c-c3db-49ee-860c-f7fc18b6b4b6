#!/usr/bin/env python3
"""
Setup script for Web Crawler
Installs dependencies and verifies installation
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies"""
    print("🔧 Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def verify_installation():
    """Verify that all modules can be imported"""
    print("\n🔍 Verifying installation...")
    
    modules_to_test = [
        ("requests", "HTTP client"),
        ("bs4", "BeautifulSoup (HTML parsing)"),
        ("selenium", "WebDriver automation"),
        ("readability", "Content extraction"),
        ("webdriver_manager", "ChromeDriver management")
    ]
    
    all_good = True
    
    for module, description in modules_to_test:
        try:
            if module == "bs4":
                import bs4
            elif module == "readability":
                from readability import Document
            elif module == "webdriver_manager":
                from webdriver_manager.chrome import ChromeDriverManager
            else:
                __import__(module)
            
            print(f"✅ {description}")
        except ImportError as e:
            print(f"❌ {description} - Import failed: {e}")
            all_good = False
        except Exception as e:
            print(f"⚠️  {description} - Warning: {e}")
    
    return all_good

def test_crawler():
    """Test basic crawler functionality"""
    print("\n🧪 Testing crawler functionality...")
    
    try:
        # Test imports
        import crawler
        import crawl4ai_engine
        import selenium_engine
        
        print("✅ All crawler modules imported successfully")
        
        # Test basic functionality with a simple URL
        print("🌐 Testing with example.com...")
        result = crawler.crawl_url("https://example.com")
        
        if result and not result.startswith("Error") and len(result) > 50:
            print("✅ Crawler test successful!")
            print(f"   Extracted {len(result)} characters")
            return True
        else:
            print(f"⚠️  Crawler test returned: {result[:100]}...")
            return False
            
    except Exception as e:
        print(f"❌ Crawler test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Web Crawler Setup")
    print("=" * 30)
    
    # Check if we're in the right directory
    required_files = ["crawler.py", "crawl4ai_engine.py", "selenium_engine.py", "requirements.txt"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        print("Please run this script from the project directory.")
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Verify installation
    if not verify_installation():
        print("\n⚠️  Some dependencies may not be properly installed.")
        print("You can still try to use the crawler, but some features may not work.")
    
    # Test crawler
    if test_crawler():
        print("\n🎉 Setup completed successfully!")
        print("\n📖 Next steps:")
        print("   1. Run: python3 crawler.py")
        print("   2. Enter a URL to crawl")
        print("   3. Type 'quit' to exit")
        print("\n📚 For detailed usage instructions, see CLI_USAGE.md")
        return True
    else:
        print("\n⚠️  Setup completed but crawler test failed.")
        print("Check your internet connection and try running the crawler manually.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
