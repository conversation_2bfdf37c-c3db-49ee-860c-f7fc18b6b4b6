# Web Crawler - Dual Engine

A robust web crawler with dual-engine architecture for maximum compatibility and performance.

## 🚀 Features

- **Dual Engine Architecture**: Fast crawl4ai engine with Selenium fallback
- **Smart Content Extraction**: Clean, readable text output using readability algorithms
- **JavaScript Support**: Handles dynamic content with Selenium WebDriver
- **Error Handling**: Graceful handling of timeouts, network errors, and invalid URLs
- **Interactive CLI**: User-friendly command-line interface
- **Auto Protocol**: Automatically adds https:// to URLs without protocol

## 📁 Project Structure

```
Crawl4ai+Selenium_quick/
├── crawler.py           # Main interactive crawler
├── crawl4ai_engine.py   # Fast HTTP-based engine
├── selenium_engine.py   # JavaScript-capable engine
├── requirements.txt     # Dependencies
└── README.md           # This file
```

## 🛠️ Installation

1. **Clone or download the project**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## 📋 Dependencies

- `requests` - HTTP client for fast crawling
- `beautifulsoup4` - HTML parsing and processing
- `selenium` - WebDriver automation for JavaScript sites
- `readability-lxml` - Content extraction and cleaning
- `webdriver-manager` - Automatic ChromeDriver management

## 🎯 How It Works

1. **Primary Engine (crawl4ai)**: Fast HTTP requests + BeautifulSoup parsing
2. **Fallback Engine (Selenium)**: Full browser rendering for JavaScript-heavy sites
3. **Content Processing**: Readability algorithm extracts main content
4. **Output**: Clean, formatted text suitable for reading or processing

## 🔧 Engine Details

### Crawl4ai Engine
- **Speed**: Very fast (HTTP requests only)
- **Use Case**: Static websites, news articles, blogs
- **Timeout**: 10 seconds
- **Advantages**: Low resource usage, high speed

### Selenium Engine
- **Speed**: Slower (full browser rendering)
- **Use Case**: JavaScript-heavy sites, SPAs, dynamic content
- **Timeout**: 15 seconds + 2 second wait for JS
- **Advantages**: Handles any website, executes JavaScript

## 📖 Usage Examples

See CLI_USAGE.md for detailed command-line usage instructions.

## 🛡️ Error Handling

The crawler handles various error scenarios:
- Network timeouts and connection errors
- HTTP errors (404, 500, etc.)
- Invalid or non-existent domains
- WebDriver initialization failures
- Empty or minimal content

## 🎨 Output Format

The crawler outputs clean, markdown-style text:
```
# Page Title

## Section Header

Main content paragraphs with proper formatting.

* List items when present
* Properly extracted and formatted

## Another Section

More content...
```

## 🔄 Workflow

1. User enters URL
2. Try crawl4ai engine (fast)
3. If successful → return clean content
4. If failed → fallback to Selenium
5. Extract and format content
6. Display results to user

## 🚀 Ready to Use!

The crawler is production-ready and thoroughly tested. Perfect for:
- Content extraction and analysis
- Web scraping projects
- Research and data collection
- Integration into larger applications
