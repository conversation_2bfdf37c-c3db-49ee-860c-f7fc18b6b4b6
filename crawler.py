#!/usr/bin/env python3
"""
Simple web crawler - Interactive mode
Takes URL input and returns clean plain text
"""

def crawl_url(url):
    """
    Crawl a URL and return clean text content
    Tries crawl4ai first, falls back to selenium if needed
    """
    # Try crawl4ai first (faster, more reliable)
    print("Trying fast engine (crawl4ai)...")
    try:
        from crawl4ai_engine import get_clean_content
        result = get_clean_content(url)
        if not result.startswith("Error"):
            print("✓ Success with fast engine")
            return result
        else:
            print(f"✗ Fast engine failed: {result[:100]}...")
    except Exception as e:
        print(f"✗ Fast engine error: {e}")
    
    # Fall back to selenium for JavaScript-heavy sites
    print("Falling back to Selenium engine (handles JavaScript)...")
    try:
        from selenium_engine import get_clean_content
        result = get_clean_content(url)
        if not result.startswith("Error"):
            print("✓ Success with Selenium engine")
            return result
        else:
            print(f"✗ Selenium engine failed: {result[:100]}...")
            return result
    except Exception as e:
        error_msg = f"Error: Both engines failed. Selenium error: {e}"
        print(f"✗ {error_msg}")
        return error_msg

def main():
    """Interactive main function"""
    print("Web Crawler")
    print("=" * 20)
    
    while True:
        try:
            url = input("\nEnter URL (or 'quit' to exit): ").strip()
            
            if url.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not url:
                print("Please enter a valid URL")
                continue
            
            # Add https:// if no protocol specified
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            print(f"\nCrawling: {url}")
            print("-" * 50)
            
            result = crawl_url(url)
            
            print("\nResult:")
            print("=" * 50)
            print(result)
            print("=" * 50)
            
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == '__main__':
    main()
