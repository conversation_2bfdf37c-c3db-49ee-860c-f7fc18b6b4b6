from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from bs4 import BeautifulSoup
from readability import Document
import time

def get_clean_content(url):
    # Try to use webdriver-manager for automatic ChromeDriver management
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        service = Service(ChromeDriverManager().install())
    except ImportError:
        service = None
    except Exception:
        service = None

    options = Options()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-logging')
    options.add_argument('--log-level=3')

    driver = None
    try:
        if service:
            driver = webdriver.Chrome(service=service, options=options)
        else:
            driver = webdriver.Chrome(options=options)

        driver.set_page_load_timeout(15)
        driver.get(url)
        time.sleep(2)  # Wait for JS to load
        html = driver.page_source

    except Exception as e:
        if driver:
            try:
                driver.quit()
            except:
                pass
        return f"Error fetching URL with Selenium: {e}"

    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass

    doc = Document(html)
    title = doc.title()
    summary_html = doc.summary()
    soup = BeautifulSoup(summary_html, 'html.parser')

    # Convert to terminal-friendly markdown-like text
    content = f"# {title}\n\n"
    for elem in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol']):
        if elem.name.startswith('h'):
            level = int(elem.name[1])
            content += f"{'#' * level} {elem.get_text(strip=True)}\n\n"
        elif elem.name == 'p':
            text = elem.get_text(strip=True)
            if text:
                content += f"{text}\n\n"
        elif elem.name in ['ul', 'ol']:
            for li in elem.find_all('li'):
                content += f"* {li.get_text(strip=True)}\n"
            content += "\n"
    return content.strip()
