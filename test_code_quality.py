#!/usr/bin/env python3
"""
Code quality and static analysis tests
Checks for common issues, imports, and code structure
"""

import sys
import os
import ast
import importlib.util

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_syntax_validity():
    """Test that all Python files have valid syntax"""
    print("Testing syntax validity...")
    
    python_files = ['crawler.py', 'crawl4ai_engine.py', 'selenium_engine.py']
    all_valid = True
    
    for filename in python_files:
        try:
            with open(filename, 'r') as f:
                source = f.read()
            
            # Parse the AST to check syntax
            ast.parse(source)
            print(f"✅ {filename}: Valid syntax")
            
        except SyntaxError as e:
            print(f"❌ {filename}: Syntax error - {e}")
            all_valid = False
        except FileNotFoundError:
            print(f"❌ {filename}: File not found")
            all_valid = False
        except Exception as e:
            print(f"❌ {filename}: Error - {e}")
            all_valid = False
    
    return all_valid

def test_imports():
    """Test that all modules can be imported successfully"""
    print("\nTesting imports...")
    
    modules = [
        ('crawler', 'crawler.py'),
        ('crawl4ai_engine', 'crawl4ai_engine.py'),
        ('selenium_engine', 'selenium_engine.py')
    ]
    
    all_imported = True
    
    for module_name, filename in modules:
        try:
            # Import the module
            spec = importlib.util.spec_from_file_location(module_name, filename)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print(f"✅ {filename}: Successfully imported")
            
        except ImportError as e:
            print(f"❌ {filename}: Import error - {e}")
            all_imported = False
        except Exception as e:
            print(f"❌ {filename}: Error - {e}")
            all_imported = False
    
    return all_imported

def test_required_functions():
    """Test that required functions exist in modules"""
    print("\nTesting required functions...")
    
    try:
        import crawler
        import crawl4ai_engine
        import selenium_engine
        
        # Check required functions
        required_functions = [
            (crawler, 'crawl_url'),
            (crawler, 'main'),
            (crawl4ai_engine, 'get_clean_content'),
            (selenium_engine, 'get_clean_content')
        ]
        
        all_present = True
        
        for module, func_name in required_functions:
            if hasattr(module, func_name):
                print(f"✅ {module.__name__}.{func_name}: Function exists")
            else:
                print(f"❌ {module.__name__}.{func_name}: Function missing")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error checking functions: {e}")
        return False

def test_dependencies():
    """Test that all required dependencies are available"""
    print("\nTesting dependencies...")
    
    dependencies = [
        'requests',
        'beautifulsoup4',
        'selenium',
        'readability',
        'webdriver_manager'
    ]
    
    all_available = True
    
    for dep in dependencies:
        try:
            if dep == 'beautifulsoup4':
                import bs4
                print(f"✅ {dep}: Available (imported as bs4)")
            elif dep == 'readability':
                from readability import Document
                print(f"✅ {dep}: Available")
            elif dep == 'webdriver_manager':
                from webdriver_manager.chrome import ChromeDriverManager
                print(f"✅ {dep}: Available")
            else:
                __import__(dep)
                print(f"✅ {dep}: Available")
                
        except ImportError:
            print(f"❌ {dep}: Not available")
            all_available = False
        except Exception as e:
            print(f"❌ {dep}: Error - {e}")
            all_available = False
    
    return all_available

def test_file_structure():
    """Test that all expected files are present"""
    print("\nTesting file structure...")
    
    expected_files = [
        'crawler.py',
        'crawl4ai_engine.py', 
        'selenium_engine.py',
        'requirements.txt'
    ]
    
    all_present = True
    
    for filename in expected_files:
        if os.path.exists(filename):
            print(f"✅ {filename}: Present")
        else:
            print(f"❌ {filename}: Missing")
            all_present = False
    
    return all_present

def main():
    """Run all code quality tests"""
    print("Code Quality Tests")
    print("=" * 30)
    
    tests = [
        test_file_structure,
        test_syntax_validity,
        test_imports,
        test_required_functions,
        test_dependencies
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n{'='*30}")
    print(f"Results: {passed}/{total} code quality tests passed")
    
    if passed == total:
        print("🎉 All code quality tests passed!")
        return True
    else:
        print("⚠️  Some tests failed - check output above")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
