#!/usr/bin/env python3
"""
Master test runner - runs all test suites and provides comprehensive results
"""

import subprocess
import sys
import time
from datetime import datetime

def run_test_suite(test_file, description):
    """Run a test suite and return results"""
    print(f"\n{'='*60}")
    print(f"Running {description}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            [sys.executable, test_file],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        success = result.returncode == 0
        
        print(f"\n{'='*60}")
        print(f"{description} - {'PASSED' if success else 'FAILED'}")
        print(f"Duration: {duration:.2f} seconds")
        print(f"{'='*60}")
        
        return success, duration
        
    except subprocess.TimeoutExpired:
        print(f"❌ {description} - TIMEOUT (exceeded 120 seconds)")
        return False, 120
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False, 0

def main():
    """Run all test suites"""
    print("🚀 COMPREHENSIVE TEST SUITE")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    test_suites = [
        ("test_code_quality.py", "Code Quality & Structure Tests"),
        ("test_crawler.py", "Unit Tests (Mocked)"),
        ("test_real_websites.py", "Real-World Integration Tests"),
        ("test_interactive.py", "Interactive Functionality Tests")
    ]
    
    results = []
    total_duration = 0
    
    for test_file, description in test_suites:
        success, duration = run_test_suite(test_file, description)
        results.append((description, success, duration))
        total_duration += duration
        
        # Small delay between test suites
        time.sleep(1)
    
    # Final summary
    print(f"\n{'='*80}")
    print("🏁 FINAL TEST RESULTS SUMMARY")
    print(f"{'='*80}")
    
    passed = 0
    total = len(results)
    
    for description, success, duration in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status:<12} {description:<40} ({duration:.2f}s)")
        if success:
            passed += 1
    
    print(f"\n{'='*80}")
    print(f"OVERALL RESULTS: {passed}/{total} test suites passed")
    print(f"Total execution time: {total_duration:.2f} seconds")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The web crawler is working perfectly!")
        print("\n✨ What was tested:")
        print("   • Code syntax and structure")
        print("   • All dependencies and imports")
        print("   • Unit tests with mocking")
        print("   • Real website crawling (both engines)")
        print("   • Error handling and edge cases")
        print("   • Interactive functionality")
        print("   • URL protocol handling")
        print("   • Fallback mechanism (crawl4ai → selenium)")
        
        print("\n🔧 Components verified:")
        print("   • crawl4ai_engine.py - Fast HTTP-based crawler")
        print("   • selenium_engine.py - JavaScript-capable crawler")
        print("   • crawler.py - Main orchestration and UI")
        print("   • All error handling and edge cases")
        
        return True
    else:
        print("⚠️  Some test suites failed - check individual results above")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
